import 'package:flutter/material.dart';
import 'package:glowing_pet_app/screens/main_screen.dart'; // Assuming your project name is 'glowing_pet_app'

void main() {
  runApp(const PetGroomingApp());
}

class PetGroomingApp extends StatelessWidget {
  const PetGroomingApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Glowing Pet',
      debugShowCheckedModeBanner: false,
      // Define the overall theme for the application based on the PDF
      theme: ThemeData(
        primarySwatch: Colors.amber,
        fontFamily: 'Poppins',
        scaffoldBackgroundColor: const Color(0xFFFFFBEF), // Light cream background
        colorScheme: const ColorScheme.light(
          primary: Color(0xFFFFC107), // Amber/Gold
          secondary: Color(0xFF00C6AE), // A complementary teal
          background: Color(0xFFFFFBEF),
          surface: Colors.white,
          onPrimary: Colors.black,
          onSecondary: Colors.black,
          onBackground: Color(0xFF3A3A3A),
          onSurface: Color(0xFF3A3A3A),
        ),
        textTheme: const TextTheme(
          displayLarge: TextStyle(fontSize: 32.0, fontWeight: FontWeight.bold, color: Color(0xFF3A3A3A)),
          headlineMedium: TextStyle(fontSize: 24.0, fontWeight: FontWeight.w600, color: Color(0xFF3A3A3A)),
          bodyLarge: TextStyle(fontSize: 16.0, color: Color(0xFF495057)),
          bodyMedium: TextStyle(fontSize: 14.0, color: Color(0xFF6C757D)),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFFC107), // Amber/Gold
            foregroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          ),
        ),
         inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(color: Colors.grey),
          ),
          filled: true,
          fillColor: Colors.white,
        ),
      ),
      // Start the app with the MainScreen widget
      home: const MainScreen(),
    );
  }
}