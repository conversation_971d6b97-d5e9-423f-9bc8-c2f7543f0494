import 'package:flutter/material.dart';
import 'package:glowing_pet_app/screens/auth/login_page.dart';

class SignUpPage extends StatelessWidget {
  const SignUpPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Account')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
           crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text('Create account', style: Theme.of(context).textTheme.headlineMedium, textAlign: TextAlign.center),
            const SizedBox(height: 32),
            TextFormField(decoration: const InputDecoration(labelText: 'First Name')),
            const SizedBox(height: 16),
            TextFormField(decoration: const InputDecoration(labelText: 'Last Name')),
            const SizedBox(height: 16),
            TextFormField(decoration: const InputDecoration(labelText: 'Email address')),
            const SizedBox(height: 16),
            TextFormField(decoration: const InputDecoration(labelText: 'Phone number')),
            const SizedBox(height: 16),
            TextFormField(decoration: const InputDecoration(labelText: 'Password'), obscureText: true),
            const SizedBox(height: 24),
            const Text('Your Pet:', style: TextStyle(fontWeight: FontWeight.bold)),
            const Row(
              children: [
                Radio(value: true, groupValue: true, onChanged: null), Text('Dog'),
                Radio(value: false, groupValue: true, onChanged: null), Text('Cat'),
                Radio(value: false, groupValue: true, onChanged: null), Text('Others'),
              ],
            ),
            const SizedBox(height: 16),
             ElevatedButton(onPressed: (){}, child: const Text('SIGN UP')),
            const SizedBox(height: 32),
             Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text("Already have an account?"),
                TextButton(onPressed: (){
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => const LoginPage()),
                  );
                }, child: const Text('Login!'))
              ],
             )
          ],
        ),
      ),
    );
  }
}
