import 'package:flutter/material.dart';
import 'package:glowing_pet_app/screens/auth/signup_page.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
             Text('Get your appointment now!', style: Theme.of(context).textTheme.headlineMedium, textAlign: TextAlign.center),
             const SizedBox(height: 32),
             TextFormField(decoration: const InputDecoration(labelText: 'Email/Username')),
             const SizedBox(height: 16),
             TextFormField(decoration: const InputDecoration(labelText: 'Password'), obscureText: true),
             const SizedBox(height: 24),
             ElevatedButton(onPressed: (){}, child: const Text('LOGIN')),
             const SizedBox(height: 16),
             TextButton(onPressed: (){}, child: const Text('Forgot?')),
             const SizedBox(height: 24),
             const Row(
              children: [
                Expanded(child: Divider()),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text('OR'),
                ),
                Expanded(child: Divider()),
              ],
             ),
             const SizedBox(height: 24),
             Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                 _buildSocialButton('G'),
                 const SizedBox(width: 16),
                 _buildSocialButton('f'),
                 const SizedBox(width: 16),
                 _buildSocialButton('X'),
              ],
             ),
             const SizedBox(height: 32),
             Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text("Don't have an account?"),
                TextButton(onPressed: (){
                   Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => const SignUpPage()),
                  );
                }, child: const Text('Sign up!'))
              ],
             )
          ],
        ),
      ),
    );
  }

  Widget _buildSocialButton(String text) {
    return OutlinedButton(
      onPressed: (){},
      style: OutlinedButton.styleFrom(
        shape: const CircleBorder(),
        padding: const EdgeInsets.all(16),
      ),
      child: Text(text, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
    );
  }
}
